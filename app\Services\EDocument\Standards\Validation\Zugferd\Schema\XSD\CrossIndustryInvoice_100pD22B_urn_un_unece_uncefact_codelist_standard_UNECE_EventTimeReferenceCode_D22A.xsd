<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm62475="urn:un:unece:uncefact:codelist:standard:UNECE:EventTimeReferenceCode:D22A"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:EventTimeReferenceCode:D22A"
    elementFormDefault="qualified"
    version="3.2">
  <xsd:simpleType name="EventTimeReferenceCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="1"/>
      <xsd:enumeration value="2"/>
      <xsd:enumeration value="3"/>
      <xsd:enumeration value="4"/>
      <xsd:enumeration value="5"/>
      <xsd:enumeration value="6"/>
      <xsd:enumeration value="7"/>
      <xsd:enumeration value="8"/>
      <xsd:enumeration value="9"/>
      <xsd:enumeration value="10"/>
      <xsd:enumeration value="11"/>
      <xsd:enumeration value="12"/>
      <xsd:enumeration value="13"/>
      <xsd:enumeration value="14"/>
      <xsd:enumeration value="21"/>
      <xsd:enumeration value="22"/>
      <xsd:enumeration value="23"/>
      <xsd:enumeration value="24"/>
      <xsd:enumeration value="25"/>
      <xsd:enumeration value="26"/>
      <xsd:enumeration value="27"/>
      <xsd:enumeration value="28"/>
      <xsd:enumeration value="29"/>
      <xsd:enumeration value="31"/>
      <xsd:enumeration value="32"/>
      <xsd:enumeration value="33"/>
      <xsd:enumeration value="41"/>
      <xsd:enumeration value="42"/>
      <xsd:enumeration value="43"/>
      <xsd:enumeration value="44"/>
      <xsd:enumeration value="45"/>
      <xsd:enumeration value="46"/>
      <xsd:enumeration value="47"/>
      <xsd:enumeration value="48"/>
      <xsd:enumeration value="52"/>
      <xsd:enumeration value="53"/>
      <xsd:enumeration value="54"/>
      <xsd:enumeration value="55"/>
      <xsd:enumeration value="56"/>
      <xsd:enumeration value="57"/>
      <xsd:enumeration value="60"/>
      <xsd:enumeration value="61"/>
      <xsd:enumeration value="62"/>
      <xsd:enumeration value="63"/>
      <xsd:enumeration value="64"/>
      <xsd:enumeration value="65"/>
      <xsd:enumeration value="66"/>
      <xsd:enumeration value="67"/>
      <xsd:enumeration value="68"/>
      <xsd:enumeration value="69"/>
      <xsd:enumeration value="70"/>
      <xsd:enumeration value="71"/>
      <xsd:enumeration value="72"/>
      <xsd:enumeration value="73"/>
      <xsd:enumeration value="74"/>
      <xsd:enumeration value="75"/>
      <xsd:enumeration value="76"/>
      <xsd:enumeration value="77"/>
      <xsd:enumeration value="78"/>
      <xsd:enumeration value="79"/>
      <xsd:enumeration value="80"/>
      <xsd:enumeration value="81"/>
      <xsd:enumeration value="82"/>
      <xsd:enumeration value="83"/>
      <xsd:enumeration value="ZZZ"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
