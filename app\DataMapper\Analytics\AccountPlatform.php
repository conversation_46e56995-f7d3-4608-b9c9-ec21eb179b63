<?php
/**
 * Invoice Ninja (https://invoiceninja.com).
 *
 * @link https://github.com/invoiceninja/invoiceninja source repository
 *
 * @copyright Copyright (c) 2025. Invoice Ninja LLC (https://invoiceninja.com)
 *
 * @license https://www.elastic.co/licensing/elastic-license
 */

namespace App\DataMapper\Analytics;

use Turbo124\Beacon\ExampleMetric\GenericMixedMetric;

class AccountPlatform extends GenericMixedMetric
{
    /**
     * The type of Sample.
     *
     * Monotonically incrementing counter
     *
     * 	- counter
     *
     * @var string
     */
    public $type = 'mixed_metric';

    /**
     * The name of the counter.
     * @var string
     */
    public $name = 'account.platform';

    /**
     * The datetime of the counter measurement.
     *
     * date("Y-m-d H:i:s")
     *
     */
    public $datetime;

    /**
     * The Class failure name
     * set to 0.
     *
     * @var string
     */
    public $string_metric5 = 'platform';

    public $string_metric6 = 'user_agent';

    public $string_metric7 = 'ip_address';

    public function __construct($string_metric5, $string_metric6, $string_metric7)
    {
        $this->string_metric5 = mb_convert_encoding($string_metric5, 'UTF-8');
        $this->string_metric6 = mb_convert_encoding($string_metric6, 'UTF-8');
        $this->string_metric7 = $string_metric7;
    }
}
