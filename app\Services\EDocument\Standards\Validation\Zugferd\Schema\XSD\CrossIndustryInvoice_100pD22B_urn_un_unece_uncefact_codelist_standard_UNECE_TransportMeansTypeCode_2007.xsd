<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm6Recommendation28="urn:un:unece:uncefact:codelist:standard:UNECE:TransportMeansTypeCode:2007"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:TransportMeansTypeCode:2007"
    elementFormDefault="qualified"
    version="3.4">
  <xsd:simpleType name="TransportMeansTypeCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="1501"/>
      <xsd:enumeration value="1502"/>
      <xsd:enumeration value="1503"/>
      <xsd:enumeration value="1504"/>
      <xsd:enumeration value="1505"/>
      <xsd:enumeration value="1506"/>
      <xsd:enumeration value="1511"/>
      <xsd:enumeration value="1512"/>
      <xsd:enumeration value="1513"/>
      <xsd:enumeration value="1514"/>
      <xsd:enumeration value="1515"/>
      <xsd:enumeration value="1516"/>
      <xsd:enumeration value="1517"/>
      <xsd:enumeration value="1518"/>
      <xsd:enumeration value="1519"/>
      <xsd:enumeration value="1521"/>
      <xsd:enumeration value="1522"/>
      <xsd:enumeration value="1523"/>
      <xsd:enumeration value="1524"/>
      <xsd:enumeration value="1525"/>
      <xsd:enumeration value="1531"/>
      <xsd:enumeration value="1532"/>
      <xsd:enumeration value="1533"/>
      <xsd:enumeration value="1534"/>
      <xsd:enumeration value="1541"/>
      <xsd:enumeration value="1542"/>
      <xsd:enumeration value="1543"/>
      <xsd:enumeration value="1551"/>
      <xsd:enumeration value="1552"/>
      <xsd:enumeration value="1553"/>
      <xsd:enumeration value="1591"/>
      <xsd:enumeration value="1592"/>
      <xsd:enumeration value="1593"/>
      <xsd:enumeration value="1594"/>
      <xsd:enumeration value="1601"/>
      <xsd:enumeration value="1602"/>
      <xsd:enumeration value="1603"/>
      <xsd:enumeration value="1604"/>
      <xsd:enumeration value="1605"/>
      <xsd:enumeration value="1606"/>
      <xsd:enumeration value="1607"/>
      <xsd:enumeration value="1711"/>
      <xsd:enumeration value="1712"/>
      <xsd:enumeration value="1721"/>
      <xsd:enumeration value="1723"/>
      <xsd:enumeration value="1724"/>
      <xsd:enumeration value="1725"/>
      <xsd:enumeration value="1726"/>
      <xsd:enumeration value="1727"/>
      <xsd:enumeration value="1728"/>
      <xsd:enumeration value="1729"/>
      <xsd:enumeration value="1751"/>
      <xsd:enumeration value="1752"/>
      <xsd:enumeration value="1753"/>
      <xsd:enumeration value="1761"/>
      <xsd:enumeration value="1762"/>
      <xsd:enumeration value="1763"/>
      <xsd:enumeration value="1764"/>
      <xsd:enumeration value="1765"/>
      <xsd:enumeration value="1766"/>
      <xsd:enumeration value="1781"/>
      <xsd:enumeration value="1782"/>
      <xsd:enumeration value="2201"/>
      <xsd:enumeration value="2202"/>
      <xsd:enumeration value="2203"/>
      <xsd:enumeration value="2301"/>
      <xsd:enumeration value="2302"/>
      <xsd:enumeration value="2303"/>
      <xsd:enumeration value="2304"/>
      <xsd:enumeration value="2305"/>
      <xsd:enumeration value="3100"/>
      <xsd:enumeration value="3101"/>
      <xsd:enumeration value="3102"/>
      <xsd:enumeration value="3103"/>
      <xsd:enumeration value="3104"/>
      <xsd:enumeration value="3105"/>
      <xsd:enumeration value="3106"/>
      <xsd:enumeration value="3107"/>
      <xsd:enumeration value="3108"/>
      <xsd:enumeration value="3109"/>
      <xsd:enumeration value="3110"/>
      <xsd:enumeration value="3111"/>
      <xsd:enumeration value="3112"/>
      <xsd:enumeration value="3113"/>
      <xsd:enumeration value="3114"/>
      <xsd:enumeration value="3115"/>
      <xsd:enumeration value="3116"/>
      <xsd:enumeration value="3117"/>
      <xsd:enumeration value="3118"/>
      <xsd:enumeration value="3119"/>
      <xsd:enumeration value="3120"/>
      <xsd:enumeration value="3121"/>
      <xsd:enumeration value="3122"/>
      <xsd:enumeration value="3123"/>
      <xsd:enumeration value="3124"/>
      <xsd:enumeration value="3125"/>
      <xsd:enumeration value="3126"/>
      <xsd:enumeration value="3127"/>
      <xsd:enumeration value="3128"/>
      <xsd:enumeration value="3129"/>
      <xsd:enumeration value="3130"/>
      <xsd:enumeration value="3131"/>
      <xsd:enumeration value="3132"/>
      <xsd:enumeration value="3133"/>
      <xsd:enumeration value="3134"/>
      <xsd:enumeration value="3135"/>
      <xsd:enumeration value="3136"/>
      <xsd:enumeration value="3137"/>
      <xsd:enumeration value="3138"/>
      <xsd:enumeration value="3201"/>
      <xsd:enumeration value="3301"/>
      <xsd:enumeration value="3302"/>
      <xsd:enumeration value="3303"/>
      <xsd:enumeration value="3304"/>
      <xsd:enumeration value="4000"/>
      <xsd:enumeration value="5000"/>
      <xsd:enumeration value="8021"/>
      <xsd:enumeration value="8022"/>
      <xsd:enumeration value="8023"/>
      <xsd:enumeration value="8161"/>
      <xsd:enumeration value="8162"/>
      <xsd:enumeration value="8163"/>
      <xsd:enumeration value="8441"/>
      <xsd:enumeration value="8442"/>
      <xsd:enumeration value="8443"/>
      <xsd:enumeration value="8444"/>
      <xsd:enumeration value="8445"/>
      <xsd:enumeration value="8446"/>
      <xsd:enumeration value="8447"/>
      <xsd:enumeration value="8448"/>
      <xsd:enumeration value="8451"/>
      <xsd:enumeration value="8452"/>
      <xsd:enumeration value="8453"/>
      <xsd:enumeration value="8454"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
