<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm61229LineStatusCode="urn:un:unece:uncefact:codelist:standard:UNECE:ActionCode:D22A"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:ActionCode:D22A"
    elementFormDefault="qualified"
    version="3.2">
  <xsd:simpleType name="ActionCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="1"/>
      <xsd:enumeration value="2"/>
      <xsd:enumeration value="3"/>
      <xsd:enumeration value="4"/>
      <xsd:enumeration value="5"/>
      <xsd:enumeration value="6"/>
      <xsd:enumeration value="7"/>
      <xsd:enumeration value="8"/>
      <xsd:enumeration value="9"/>
      <xsd:enumeration value="10"/>
      <xsd:enumeration value="11"/>
      <xsd:enumeration value="12"/>
      <xsd:enumeration value="13"/>
      <xsd:enumeration value="14"/>
      <xsd:enumeration value="15"/>
      <xsd:enumeration value="16"/>
      <xsd:enumeration value="17"/>
      <xsd:enumeration value="18"/>
      <xsd:enumeration value="19"/>
      <xsd:enumeration value="20"/>
      <xsd:enumeration value="21"/>
      <xsd:enumeration value="22"/>
      <xsd:enumeration value="23"/>
      <xsd:enumeration value="24"/>
      <xsd:enumeration value="25"/>
      <xsd:enumeration value="26"/>
      <xsd:enumeration value="27"/>
      <xsd:enumeration value="28"/>
      <xsd:enumeration value="29"/>
      <xsd:enumeration value="30"/>
      <xsd:enumeration value="31"/>
      <xsd:enumeration value="32"/>
      <xsd:enumeration value="33"/>
      <xsd:enumeration value="34"/>
      <xsd:enumeration value="35"/>
      <xsd:enumeration value="36"/>
      <xsd:enumeration value="37"/>
      <xsd:enumeration value="38"/>
      <xsd:enumeration value="39"/>
      <xsd:enumeration value="40"/>
      <xsd:enumeration value="41"/>
      <xsd:enumeration value="42"/>
      <xsd:enumeration value="43"/>
      <xsd:enumeration value="44"/>
      <xsd:enumeration value="45"/>
      <xsd:enumeration value="46"/>
      <xsd:enumeration value="47"/>
      <xsd:enumeration value="48"/>
      <xsd:enumeration value="49"/>
      <xsd:enumeration value="50"/>
      <xsd:enumeration value="51"/>
      <xsd:enumeration value="52"/>
      <xsd:enumeration value="53"/>
      <xsd:enumeration value="54"/>
      <xsd:enumeration value="55"/>
      <xsd:enumeration value="56"/>
      <xsd:enumeration value="57"/>
      <xsd:enumeration value="58"/>
      <xsd:enumeration value="59"/>
      <xsd:enumeration value="60"/>
      <xsd:enumeration value="61"/>
      <xsd:enumeration value="62"/>
      <xsd:enumeration value="63"/>
      <xsd:enumeration value="64"/>
      <xsd:enumeration value="65"/>
      <xsd:enumeration value="66"/>
      <xsd:enumeration value="67"/>
      <xsd:enumeration value="68"/>
      <xsd:enumeration value="69"/>
      <xsd:enumeration value="70"/>
      <xsd:enumeration value="71"/>
      <xsd:enumeration value="72"/>
      <xsd:enumeration value="73"/>
      <xsd:enumeration value="74"/>
      <xsd:enumeration value="75"/>
      <xsd:enumeration value="76"/>
      <xsd:enumeration value="77"/>
      <xsd:enumeration value="78"/>
      <xsd:enumeration value="79"/>
      <xsd:enumeration value="80"/>
      <xsd:enumeration value="81"/>
      <xsd:enumeration value="82"/>
      <xsd:enumeration value="83"/>
      <xsd:enumeration value="84"/>
      <xsd:enumeration value="85"/>
      <xsd:enumeration value="86"/>
      <xsd:enumeration value="87"/>
      <xsd:enumeration value="88"/>
      <xsd:enumeration value="89"/>
      <xsd:enumeration value="90"/>
      <xsd:enumeration value="91"/>
      <xsd:enumeration value="92"/>
      <xsd:enumeration value="93"/>
      <xsd:enumeration value="94"/>
      <xsd:enumeration value="95"/>
      <xsd:enumeration value="96"/>
      <xsd:enumeration value="97"/>
      <xsd:enumeration value="98"/>
      <xsd:enumeration value="99"/>
      <xsd:enumeration value="100"/>
      <xsd:enumeration value="101"/>
      <xsd:enumeration value="102"/>
      <xsd:enumeration value="103"/>
      <xsd:enumeration value="104"/>
      <xsd:enumeration value="105"/>
      <xsd:enumeration value="106"/>
      <xsd:enumeration value="107"/>
      <xsd:enumeration value="108"/>
      <xsd:enumeration value="109"/>
      <xsd:enumeration value="110"/>
      <xsd:enumeration value="111"/>
      <xsd:enumeration value="112"/>
      <xsd:enumeration value="113"/>
      <xsd:enumeration value="114"/>
      <xsd:enumeration value="115"/>
      <xsd:enumeration value="116"/>
      <xsd:enumeration value="117"/>
      <xsd:enumeration value="118"/>
      <xsd:enumeration value="119"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
