<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm68339="urn:un:unece:uncefact:codelist:standard:UNECE:DangerousGoodsPackingCode:D22A"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:DangerousGoodsPackingCode:D22A"
    elementFormDefault="qualified"
    version="3.2">
  <xsd:simpleType name="DangerousGoodsPackingCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="1"/>
      <xsd:enumeration value="2"/>
      <xsd:enumeration value="3"/>
      <xsd:enumeration value="4"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
