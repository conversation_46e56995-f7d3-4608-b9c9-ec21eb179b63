<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:ram="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:qdt="urn:un:unece:uncefact:data:standard:QualifiedDataType:100"
    xmlns:udt="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100"
    targetNamespace="urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:100"
    elementFormDefault="qualified"
    version="100.D22B">
  <xsd:import namespace="urn:un:unece:uncefact:data:standard:QualifiedDataType:100" schemaLocation="CrossIndustryInvoice_100pD22B_urn_un_unece_uncefact_data_standard_QualifiedDataType_100.xsd"/>
  <xsd:import namespace="urn:un:unece:uncefact:data:standard:UnqualifiedDataType:100" schemaLocation="CrossIndustryInvoice_100pD22B_urn_un_unece_uncefact_data_standard_UnqualifiedDataType_100.xsd"/>
  <xsd:complexType name="AdvancePaymentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Advance Payment</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="PaidAmount" type="udt:AmountType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Paid Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FormattedReceivedDateTime" type="qdt:FormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Formatted Received Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceSpecifiedReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AppliedAllowanceChargeType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Applied Allowance/Charge</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ActualAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeIndicator" type="udt:IndicatorType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryAppliedTax" type="ram:AppliedTaxType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Category</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AppliedTaxType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Applied Tax</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="CalculatedAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculated Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculatedRate" type="udt:RateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculated Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxPointDate" type="udt:DateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Point Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="AvailablePeriodType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Available Period</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="StartDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Start Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EndDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">End Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="BasicWorkItemType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Basic Work Item</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferenceID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reference ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PrimaryClassificationCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Primary Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AlternativeClassificationCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Alternative Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Comment" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Comment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalQuantityClassificationCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Quantity Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Index" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Index Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestedActionCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requested Action Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PriceListItemID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Price List Item ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualWorkItemComplexDescription" type="ram:WorkItemComplexDescriptionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Complex Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalQuantityWorkItemQuantityAnalysis" type="ram:WorkItemQuantityAnalysisType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Quantity Analysis</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UnitCalculatedPrice" type="ram:CalculatedPriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculated Unit Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalCalculatedPrice" type="ram:CalculatedPriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculated Total Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangedRecordedStatus" type="ram:RecordedStatusType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Recorded Changed Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemBasicWorkItem" type="ram:BasicWorkItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Item Basic Work Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferencedSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Referenced Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="BranchFinancialInstitutionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Branch Financial Institution</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sort Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LocationFinancialInstitutionAddress" type="ram:FinancialInstitutionAddressType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CalculatedPriceType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Calculated Price</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RelatedAppliedAllowanceCharge" type="ram:AppliedAllowanceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applied Allowance/Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ContactPersonType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Contact Person</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="GivenName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Given Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MiddleName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Middle Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FamilyName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Family Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CreditorFinancialAccountType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Creditor Financial Account</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="IBANID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">IBAN ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AccountName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProprietaryID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Proprietary ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CreditorFinancialInstitutionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Creditor Financial Institution</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="BICID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">BIC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CHIPSUniversalID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">CHIPS Universal ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NewZealandNCCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">New Zealand NCC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IrishNSCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Irish NSC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UKSortCodeID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">UK Sort Code ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CHIPSParticipantID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">CHIPS Participant ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SwissBCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Swiss BC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FedwireRoutingNumberID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Fedwire Routing Number ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PortugueseNCCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Portuguese NCC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RussianCentralBankID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Russian Central Bank ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItalianDomesticID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Italian Domestic ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AustrianBankleitzahlID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Austrian Bankleitzahl ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CanadianPaymentsAssociationID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Canadian Payments Association ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SICID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">SIC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GermanBankleitzahlID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">German Bankleitzahl ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpanishDomesticInterbankingID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Spanish Domestic Interbanking ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SouthAfricanNCCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">South African NCC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="HongKongBankID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Hong Kong Bank ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AustralianBSBID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Australian BSB ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IndianFinancialSystemID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Indian Financial System ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="HellenicBankID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Hellenic Bank ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PolishNationalClearingID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Polish National Clearing ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ClearingSystemName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Clearing System Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="JapanFinancialInstitutionCommonID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Japan Financial Institution Common ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LocationFinancialInstitutionAddress" type="ram:FinancialInstitutionAddressType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubDivisionBranchFinancialInstitution" type="ram:BranchFinancialInstitutionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Branch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CrossBorderCustomsValuationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Cross-Border Customs Valuation</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="AddedAdjustmentAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Added Adjustment Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeductedAdjustmentAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Deducted Adjustment Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AddedAdjustmentPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Added Adjustment Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeductedAdjustmentPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Deducted Adjustment Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="WTOAdditionCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">WTO Addition Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeApportionMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Apportion Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OtherChargeAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Other Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerSellerRelationshipIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Seller Relationship Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerSellerRelationshipPriceInfluenceIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Seller Relationship Price Influence Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SaleRestrictionIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sale Restriction Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SalePriceConditionIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sale Price Condition Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RoyaltyLicenseFeeIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Royalty Licence Fee Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SaleRestriction" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sale Restriction Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeCurrencyExchange" type="ram:TradeCurrencyExchangeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Currency Exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="CrossBorderRegulatoryProcedureType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Cross-Border Regulatory Procedure</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TransactionNatureCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Transaction Nature Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TariffAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tariff Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NonTariffChargeAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Non-Tariff Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalChargeAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Remark" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Remark</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DebtorFinancialAccountType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Debtor Financial Account</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="IBANID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">IBAN ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AccountName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProprietaryID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Proprietary ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DebtorFinancialInstitutionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Debtor Financial Institution</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="BICID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">BIC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ClearingSystemName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Clearing System Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CHIPSUniversalID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">CHIPS Universal ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NewZealandNCCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">New Zealand NCC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IrishNSCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Irish NSC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UKSortCodeID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">UK Sort Code ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CHIPSParticipantID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">CHIPS Participant ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SwissBCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Swiss BC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FedwireRoutingNumberID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Fedwire Routing Number ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PortugueseNCCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Portuguese NCC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RussianCentralBankID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Russian Central Bank ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItalianDomesticID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Italian Domestic ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AustrianBankleitzahlID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Austrian Bankleitzahl ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CanadianPaymentsAssociationID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Canadian Payments Association ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SICID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">SIC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GermanBankleitzahlID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">German Bankleitzahl ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpanishDomesticInterbankingID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Spanish Domestic Interbanking ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SouthAfricanNCCID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">South African NCC ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="HongKongBankID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Hong Kong Bank ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AustralianBSBID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Australian BSB ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IndianFinancialSystemID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Indian Financial System ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="HellenicBankID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Hellenic Bank ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PolishNationalClearingID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Polish National Clearing ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="JapanFinancialInstitutionCommonID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Japan Financial Institution Common ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LocationFinancialInstitutionAddress" type="ram:FinancialInstitutionAddressType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubDivisionBranchFinancialInstitution" type="ram:BranchFinancialInstitutionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Branch</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DeliveryAdjustmentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Delivery Adjustment</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ReasonCode" type="qdt:AdjustmentReasonCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Reason" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DisposalInstructionsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Disposal Instructions</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="MaterialID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Material ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RecyclingDescriptionCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Recycling Description Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RecyclingProcedure" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Recycling Procedure Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DocumentAuthenticationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Document Authentication</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ActualDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Information" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Signatory" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Signatory Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SignatoryImageBinaryObject" type="udt:BinaryObjectType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Signatory Image Binary Object</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DocumentContextParameterType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Document Context Parameter</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Value" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedDocumentVersion" type="ram:DocumentVersionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Version</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DocumentLineDocumentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Document Line</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="LineID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ParentLineID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Parent Line ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineStatusCode" type="qdt:LineStatusCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Status Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineStatusReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Status Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ResponseReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Response Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reference Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="DocumentVersionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Document Version</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IssueDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issue Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ExchangedDocumentContextType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Exchanged Document Context</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="SpecifiedTransactionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Transaction ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TestIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Test Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BusinessProcessSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Business Process</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BIMSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">BIM</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ScenarioSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Scenario</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicationSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Application</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GuidelineSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Guideline</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubsetSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subset</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MessageStandardSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Message Standard</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UserSpecifiedDocumentContextParameter" type="ram:DocumentContextParameterType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">User Specified Parameter</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ExchangedDocumentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Exchanged Document</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:DocumentCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IssueDateTime" type="udt:DateTimeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issue Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CopyIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation/>
      </xsd:element>
      <xsd:element name="Purpose" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Purpose Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ControlRequirementIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Control Requirement Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LanguageID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PurposeCode" type="qdt:MessageFunctionCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Purpose Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RevisionDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Revision Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VersionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Version ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GlobalID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RevisionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Revision ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PreviousRevisionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Previous Revision ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestedResponseTypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Response Request Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreationDateTime" type="qdt:FormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creation Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FirstVersionIssueDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">First Version Issue Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubtypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subtype Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reference Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SignatoryDocumentAuthentication" type="ram:DocumentAuthenticationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Signatory Authentication</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EffectiveSpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Effective Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IssuerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issuer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AttachedSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Attached File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FinancialAdjustmentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Financial Adjustment</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Reason" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DirectionCode" type="qdt:AccountingDebitCreditStatusCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Direction Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ClaimRelatedTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Claim Related Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reference Invoice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RelatedTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Related Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="FinancialInstitutionAddressType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Financial Institution Address</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="PostcodeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Postcode</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuildingNumber" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Building Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineOne" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line One</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineTwo" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Two</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineThree" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Three</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineFour" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Four</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineFive" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Five</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StreetName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Street Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CityName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">City Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountrySubDivisionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Sub-Division Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountryID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DepartmentName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Department Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PostOfficeBox" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Post Office Box</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CityID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">City ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountrySubDivisionName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Sub-Division Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountryName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GeographicalCoordinateType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Geographical Coordinate</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="AltitudeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Altitude</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LatitudeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Latitude</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LongitudeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Longitude</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SystemID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">System ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="GroupedWorkItemType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Grouped Work Item</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PrimaryClassificationCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Primary Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AlternativeClassificationCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Alternative Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Comment" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Comment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Index" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Index Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestedActionCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requested Action Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PriceListItemID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Price List Item ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalCalculatedPrice" type="ram:CalculatedPriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Calculated Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemGroupedWorkItem" type="ram:GroupedWorkItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Item Grouped Work Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemBasicWorkItem" type="ram:BasicWorkItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Item Basic Work Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangedRecordedStatus" type="ram:RecordedStatusType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Changed Recorded Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualWorkItemComplexDescription" type="ram:WorkItemComplexDescriptionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Complex Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferencedSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="HeaderTradeAgreementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Header Trade Agreement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="Reference" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerReference" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SalesAgentTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sales Agent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerRequisitionerTradeParty" type="ram:TradePartyType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Requisitioner</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerAssignedAccountantTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Assigned Accountant</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerAssignedAccountantTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Assigned Accountant</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerTaxRepresentativeTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Tax Representative</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerTaxRepresentativeTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Tax Representative</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductEndUserTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">End User</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeDeliveryTerms" type="ram:TradeDeliveryTermsType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Delivery Terms</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="QuotationReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Quotation Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OrderResponseReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Order Response Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contract Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DemandForecastReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Demand Forecast Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SupplyInstructionReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Supply Instruction Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PromotionalDealReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Promotional Deal Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PriceListReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Price List Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequisitionerReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requisitioner Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerAgentTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Agent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PurchaseConditionsReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Purchase Conditions Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedProcuringProject" type="ram:ProcuringProjectType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Procuring Project</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UltimateCustomerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ultimate Customer Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PricingBaseApplicableLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Pricing Base Applicable Logistics Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="HeaderTradeDeliveryType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Header Trade Delivery</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="RelatedSupplyChainConsignment" type="ram:SupplyChainConsignmentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Related Consignment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ShipToTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ship To Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UltimateShipToTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ultimate Ship To Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ShipFromTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ship From Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDespatchSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Despatch Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualPickUpSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Pick-Up Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDeliverySupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Delivery Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualReceiptSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Receipt Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DespatchAdviceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Despatch Advice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReceivingAdviceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Receiving Advice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeliveryNoteReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Delivery Note Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConsumptionReportReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Consumption Report Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PreviousDeliverySupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Previous Delivery Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PackingListReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Packing List Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="HeaderTradeSettlementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Header Trade Settlement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="DuePayableAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Due Payable Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditorReferenceTypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creditor Reference Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditorReferenceType" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creditor Reference Type Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditorReferenceIssuerID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creditor Reference Issuer ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditorReferenceID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creditor Reference ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentReference" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxCurrencyCode" type="qdt:CurrencyCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceCurrencyCode" type="qdt:CurrencyCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentCurrencyCode" type="qdt:CurrencyCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceIssuerReference" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Issuer Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NextInvoiceDateTime" type="udt:DateTimeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Next Invoice Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Credit Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditReason" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Credit Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoicerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoicer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceeTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoicee</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayeeTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payee</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxApplicableTradeCurrencyExchange" type="ram:TradeCurrencyExchangeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Currency Exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceApplicableTradeCurrencyExchange" type="ram:TradeCurrencyExchangeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Currency Exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentApplicableTradeCurrencyExchange" type="ram:TradeCurrencyExchangeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Currency Exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeSettlementPaymentMeans" type="ram:TradeSettlementPaymentMeansType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Means</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BillingSpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Billing Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeAllowanceCharge" type="ram:TradeAllowanceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Allowance/Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubtotalCalculatedTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subtotal Calculated Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLogisticsServiceCharge" type="ram:LogisticsServiceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Logistics Service Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradePaymentTerms" type="ram:TradePaymentTermsType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Terms</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeSettlementHeaderMonetarySummation" type="ram:TradeSettlementHeaderMonetarySummationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Monetary Summation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedFinancialAdjustment" type="ram:FinancialAdjustmentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Financial Adjustment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProFormaInvoiceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Pro-Forma Invoice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LetterOfCreditReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Letter Of Credit Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FactoringAgreementReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Factoring Agreement Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FactoringListReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Factoring List Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayableSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Accounts Payable</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReceivableSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Accounts Receivable</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PurchaseSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Purchase Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SalesSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sales Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeSettlementFinancialCard" type="ram:TradeSettlementFinancialCardType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Financial Card</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedAdvancePayment" type="ram:AdvancePaymentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Advance Payment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UltimatePayeeTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ultimate Payee</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LegalOrganizationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Legal Organization</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="LegalClassificationCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Legal Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TradingBusinessName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trading Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PostalTradeAddress" type="ram:TradeAddressType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Postal Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AuthorizedLegalRegistration" type="ram:LegalRegistrationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Authorized Registration</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LegalRegistrationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Legal Registration</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LineTradeAgreementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Line Trade Agreement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="BuyerReference" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerRequisitionerTradeParty" type="ram:TradePartyType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Requisitioner</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeDeliveryTerms" type="ram:TradeDeliveryTermsType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Delivery Terms</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="QuotationReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Quotation Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contract Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DemandForecastReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Demand Forecast Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PromotionalDealReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Promotional Deal Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossPriceProductTradePrice" type="ram:TradePriceType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Gross Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetPriceProductTradePrice" type="ram:TradePriceType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Net Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequisitionerReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requisitioner Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemSellerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Item Seller</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemBuyerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Item Buyer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedSpecifiedMarketplace" type="ram:SpecifiedMarketplaceType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Marketplace</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UltimateCustomerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ultimate Customer Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LineTradeDeliveryType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Line Trade Delivery</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="RequestedQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requested Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReceivedQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Received Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BilledQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Billed Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeFreeQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Free Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PackageQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Package Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PerPackageUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Per Package Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TheoreticalWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Theoretical Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DespatchedQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Despatched Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedDeliveryAdjustment" type="ram:DeliveryAdjustmentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Delivery Adjustment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedSupplyChainPackaging" type="ram:SupplyChainPackagingType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Packaging</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RelatedSupplyChainConsignment" type="ram:SupplyChainConsignmentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Related Consignment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ShipToTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ship To Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UltimateShipToTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ultimate Ship To Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ShipFromTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ship From Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDespatchSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Despatch Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualPickUpSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Pick-Up Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestedDeliverySupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requested Delivery Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDeliverySupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Delivery Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualReceiptSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Receipt Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DespatchAdviceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Despatch Advice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReceivingAdviceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Receiving Advice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeliveryNoteReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Delivery Note Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConsumptionReportReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Consumption Report Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PackingListReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Packing List Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LineTradeSettlementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Line Trade Settlement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="PaymentReference" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceIssuerReference" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Issuer Reference Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalAdjustmentAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Adjustment Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DiscountIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Discount Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BillingSpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Billing Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeAllowanceCharge" type="ram:TradeAllowanceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Allowance/Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubtotalCalculatedTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subtotal Calculated Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLogisticsServiceCharge" type="ram:LogisticsServiceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Logistics Service Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradePaymentTerms" type="ram:TradePaymentTermsType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Terms</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeSettlementLineMonetarySummation" type="ram:TradeSettlementLineMonetarySummationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Monetary Summation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedFinancialAdjustment" type="ram:FinancialAdjustmentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Financial Adjustment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayableSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Accounts Payable</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReceivableSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Accounts Receivable</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PurchaseSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Purchase Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SalesSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sales Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeSettlementFinancialCard" type="ram:TradeSettlementFinancialCardType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Financial Card</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LogisticsLocationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Logistics Location</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:LocationFunctionCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountryID" type="qdt:CountryIDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountrySubDivisionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Sub-Division ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PhysicalGeographicalCoordinate" type="ram:GeographicalCoordinateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Geographical Coordinates</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PostalTradeAddress" type="ram:TradeAddressType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Postal Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubordinateLocation" type="ram:SubordinateLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subordinate Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LogisticsSealType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Logistics Seal</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MaximumID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Maximum ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConditionCode" type="qdt:SealConditionCodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Condition Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SealingPartyRoleCode" type="qdt:SealingPartyRoleCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sealing Party Role Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IssuingTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issuing Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LogisticsServiceChargeType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Logistics Service Charge</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="qdt:FreightChargeTypeIDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentArrangementCode" type="qdt:TransportServicePaymentArrangementCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Arrangement Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TariffClassCode" type="qdt:FreightChargeTariffClassCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tariff Class Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeCategoryCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ServiceCategoryCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Service Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DisbursementAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Disbursement Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AppliedAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applied Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AllowanceCharge" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Allowance/Charge Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayingPartyRoleCode" type="qdt:ChargePayingPartyRoleCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Paying Party Role Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationBasisCode" type="qdt:LogisticsChargeCalculationBasisCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Basis Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationBasis" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Basis Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TransportPaymentMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Transport Payment Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentPlaceLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Place</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AppliedFromLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applied From Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AppliedToLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applied To Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AppliedTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LogisticsTransportEquipmentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Logistics Transport Equipment</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LoadingLengthMeasure" type="qdt:LinearUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Loading Length</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="qdt:TransportEquipmentCategoryCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CharacteristicCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Size/Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Characteristic" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Characteristic Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UsedCapacityCode" type="qdt:TransportEquipmentFullnessCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Full/Empty Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CarrierAssignedBookingID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Carrier Assigned Booking ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SealedIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sealed Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReturnableIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Returnable Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AffixedLogisticsSeal" type="ram:LogisticsSealType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Affixed Seal</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LogisticsTransportMeansType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Logistics Transport Means</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="qdt:TransportMeansTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Type" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OwnerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Owner</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="LogisticsTransportMovementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Logistics Transport Movement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="StageCode" type="qdt:TransportMovementStageCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Stage Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ModeCode" type="qdt:TransportModeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Mode Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Mode" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Mode Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StatusCode" type="qdt:StatusCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Status Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ServiceCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Service Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Service" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Service Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Type" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Cycle" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Cycle Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UsedLogisticsTransportMeans" type="ram:LogisticsTransportMeansType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Used Transport Means</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="MaterialGoodsCharacteristicType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Material Goods Characteristic</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProportionalConstituentPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Constituent Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AbsolutePresenceWeightMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Absolute Presence Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AbsolutePresenceVolumeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Absolute Presence Volume</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="NoteType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Note</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="Subject" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subject Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Content" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubjectCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subject Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="PackagingMarkingType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Packaging Marking</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="qdt:PackagingMarkingCodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Content" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BarcodeTypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Barcode Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AutomaticDataCaptureMethodTypeCode" type="qdt:AutomaticDataCaptureMethodCodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Automatic Data Capture Method Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProcuringProjectType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Procuring Project</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProductCharacteristicConditionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Product Characteristic Condition</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProductCharacteristicType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Product Characteristic</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MeasurementMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Measurement Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Value" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentTypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableProductCharacteristicCondition" type="ram:ProductCharacteristicConditionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Condition</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableReferencedStandard" type="ram:ReferencedStandardType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Referenced Standard</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ProductClassificationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Product Classification</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="SystemID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">System ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SystemName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">System Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ClassCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Class Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ClassName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Class Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubClassCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sub-Class Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ClassProductCharacteristic" type="ram:ProductCharacteristicType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Class Characteristic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableReferencedStandard" type="ram:ReferencedStandardType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Referenced Standard</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RecordedStatusType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Recorded Status</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ConditionCode" type="udt:CodeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Condition Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangerName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Changer Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangedDateTime" type="udt:DateTimeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Changed Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ReferencedDocumentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Referenced Document</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="IssuerAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issuer Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="URIID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">URI</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StatusCode" type="qdt:DocumentStatusCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Status Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CopyIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Copy Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:DocumentCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GlobalID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RevisionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Revision ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReceiptDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Receipt Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AttachmentBinaryObject" type="udt:BinaryObjectType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Attached Binary Object</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Information" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferenceTypeCode" type="qdt:ReferenceCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reference Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SectionName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Section Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PreviousRevisionID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Previous Revision ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FormattedIssueDateTime" type="qdt:FormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Formatted Issue Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PageID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Page ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubordinateLineID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subordinate Line ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubtypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subtype Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EffectiveSpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Effective Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IssuerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issuer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AttachedSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Attached Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ReferencedProductType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Referenced Product</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GlobalID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ManufacturerAssignedID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Manufacturer Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IndustryAssignedID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Industry Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RelationshipTypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Relationship Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UnitQuantity" type="udt:QuantityType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ReferencedStandardType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Referenced Standard</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VersionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Version ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ElementVersionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Element Version ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="URIID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">URI</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PartID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Part ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AgencyID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Agency ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ReferencePriceType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Reference Price</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ChargeAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisQuantity" type="udt:QuantityType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetPriceIndicator" type="udt:IndicatorType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Price Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ComparisonMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Comparison Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="RegisteredTaxType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Registered Tax</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExemptionReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Exemption Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExemptionReason" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Exemption Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CurrencyCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Jurisdiction" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Jurisdiction Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CustomsDutyIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customs Duty Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ReturnableAssetInstructionsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Returnable Asset Instructions</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="MaterialID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Material ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TermsAndConditionsDescription" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Terms And Conditions Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TermsAndConditionsDescriptionCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Terms And Conditions Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DepositValueSpecifiedAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Deposit Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DepositValueValiditySpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Deposit Validity Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SpatialDimensionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Spatial Dimensions</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ValueMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Value Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:DimensionTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="WidthMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Width</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LengthMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Length</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="HeightMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Height</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DiameterMeasure" type="qdt:LinearUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Diameter</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SpecificationQueryType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Specification Query</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Content" type="udt:TextType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SpecificationResponseType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Specification Response</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="QueryID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Query ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Content" type="udt:TextType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SpecifiedBinaryFileType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Binary File</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Title" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Title</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AuthorName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Author Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VersionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Version ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FileName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="URIID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">URI</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MIMECode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">MIME Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EncodingCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Encoding Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CharacterSetCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Character Set Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedBinaryObject" type="udt:BinaryObjectType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Binary Object</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Access" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Access Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SizeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Size</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AccessAvailabilitySpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Access Availability Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SpecifiedMarketplaceType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Marketplace</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VirtualIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Virtual Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="WebsiteURIID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Website</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SalesMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sales Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OrderingAvailablePeriod" type="ram:AvailablePeriodType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Available Ordering Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SpecifiedPeriodType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Period</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="DurationMeasure" type="udt:MeasureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Duration Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InclusiveIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Inclusive Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StartDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Start Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EndDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">End Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CompleteDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Complete Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OpenIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Open Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SeasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Season Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SequenceNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sequence Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StartDateFlexibilityCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Start Date Flexibility Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContinuousIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Continuous Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PurposeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Purpose Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SubordinateLineTradeAgreementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Subordinate Line Trade Agreement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="SellerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerOrderReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Order Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossPriceProductTradePrice" type="ram:TradePriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetPriceProductTradePrice" type="ram:TradePriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SubordinateLineTradeDeliveryType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Subordinate Line Trade Delivery</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="PackageQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Package Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PerPackageUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Per Package Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BilledQuantity" type="udt:QuantityType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Billed Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedSupplyChainPackaging" type="ram:SupplyChainPackagingType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Packaging</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDeliverySupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Delivery Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SubordinateLineTradeSettlementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Subordinate Line Trade Settlement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="AmountDirectionCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Amount Direction Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeTax" type="ram:TradeTaxType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedFinancialAdjustment" type="ram:FinancialAdjustmentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Specified Financial Adjustment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeAllowanceCharge" type="ram:TradeAllowanceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Specified Allowance Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SubordinateLocationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Subordinate Location</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:LocationFunctionCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PhysicalGeographicalCoordinate" type="ram:GeographicalCoordinateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Physical Geographical Coordinate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SubordinateTradeLineItemType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Subordinate Trade Line Item</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ResponseReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Response Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedReferencedProduct" type="ram:ReferencedProductType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Referenced Product</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeProduct" type="ram:TradeProductType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Product</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedSubordinateLineTradeAgreement" type="ram:SubordinateLineTradeAgreementType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subordinate Line Trade Agreement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedSubordinateLineTradeDelivery" type="ram:SubordinateLineTradeDeliveryType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subordinate Line Trade Delivery</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedSubordinateLineTradeSettlement" type="ram:SubordinateLineTradeSettlementType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subordinate Line Trade Settlement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SupplyChainConsignmentItemType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Supply Chain Consignment Item</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="qdt:GoodsTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Goods Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeExtensionCode" type="qdt:GoodsTypeExtensionCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Goods Type Extension Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeclaredValueForCustomsAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Declared Value For Customs Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeclaredValueForStatisticsAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Declared Value For Statistics Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InvoiceAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Invoice Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TariffQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tariff Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GlobalID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NatureIdentificationTransportCargo" type="ram:TransportCargoType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Cargo Nature Identification</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTransportDangerousGoods" type="ram:TransportDangerousGoodsType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Transport Dangerous Goods</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PreviousAdministrativeReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Previous Administrative Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SupplyChainConsignmentType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Supply Chain Consignment</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossVolumeMeasure" type="qdt:VolumeUnitMeasureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Volume</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeableWeightMeasure" type="qdt:WeightUnitMeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Chargeable Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InsurancePremiumAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Insurance Premium Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AssociatedInvoiceAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Associated Invoice Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalChargeAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeclaredValueForCustomsAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Declared Value For Customs Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PackageQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Number Of Packages</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetVolumeMeasure" type="qdt:VolumeUnitMeasureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Volume Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConsignorTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Consignor</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConsigneeTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Consignee</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CarrierTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Carrier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FreightForwarderTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Freight Forwarder</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeliveryTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Delivery Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CustomsImportAgentTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customs Import Agent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CustomsExportAgentTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customs Export Agent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GroupingCentreTradeParty" type="ram:TradePartyType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Grouping Centre</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TransitLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Transit Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TransportContractReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Transport Contract Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AssociatedReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Associated Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedSupplyChainConsignmentItem" type="ram:SupplyChainConsignmentItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Consignment Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UtilizedLogisticsTransportEquipment" type="ram:LogisticsTransportEquipmentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Used Transport Equipment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLogisticsTransportMovement" type="ram:LogisticsTransportMovementType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Specified Transport Movement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTransportCargoInsurance" type="ram:TransportCargoInsuranceType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Cargo Insurance</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableCrossBorderRegulatoryProcedure" type="ram:CrossBorderRegulatoryProcedureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Regulatory Procedure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableCrossBorderCustomsValuation" type="ram:CrossBorderCustomsValuationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customs Valuation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SupplyChainEventType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Supply Chain Event</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OccurrenceDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Occurrence Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DescriptionBinaryObject" type="udt:BinaryObjectType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description Binary Object</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LatestOccurrenceDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Latest Occurrence Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EarliestOccurrenceDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Earliest Occurrence Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TimeOccurrenceDateTime" type="qdt:TimeOnlyFormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Occurrence Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OccurrenceSpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Occurrence Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OccurrenceLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SupplyChainPackagingType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Supply Chain Packaging</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="qdt:PackageTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Type" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConditionCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Condition Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DisposalMethodCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Disposal Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="WeightMeasure" type="udt:MeasureType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MaximumStackabilityQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Maximum Stackability Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MaximumStackabilityWeightMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Maximum Stackability Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CustomerFacingTotalUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customer Facing Total Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LayerTotalUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Layer Total Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentLayerQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Layer Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalInstructionCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Instruction Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalInstructionIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Instruction Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MinimumLinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Minimum Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MaximumLinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Maximum Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedPackagingMarking" type="ram:PackagingMarkingType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Marking</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableMaterialGoodsCharacteristic" type="ram:MaterialGoodsCharacteristicType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Goods Characteristic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableDisposalInstructions" type="ram:DisposalInstructionsType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Disposal Instructions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableReturnableAssetInstructions" type="ram:ReturnableAssetInstructionsType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Returnable Asset Instructions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SupplyChainTradeLineItemType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Supply Chain Trade Line Item</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="DescriptionCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AssociatedDocumentLineDocument" type="ram:DocumentLineDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Associated Document Line</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeProduct" type="ram:TradeProductType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Product</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalInformationNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Information Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLineTradeAgreement" type="ram:LineTradeAgreementType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Trade Agreement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLineTradeDelivery" type="ram:LineTradeDeliveryType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Trade Delivery</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLineTradeSettlement" type="ram:LineTradeSettlementType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Trade Settlement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedSubordinateTradeLineItem" type="ram:SubordinateTradeLineItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Subordinate Trade Line Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="SupplyChainTradeTransactionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Supply Chain Trade Transaction</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="IncludedSupplyChainTradeLineItem" type="ram:SupplyChainTradeLineItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Trade Line Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableHeaderTradeAgreement" type="ram:HeaderTradeAgreementType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Header Trade Agreement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableHeaderTradeDelivery" type="ram:HeaderTradeDeliveryType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Header Trade Delivery</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableHeaderTradeSettlement" type="ram:HeaderTradeSettlementType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Header Trade Settlement</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TaxRegistrationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Tax Registration</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IOSSID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">IOSS ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AssociatedRegisteredTax" type="ram:RegisteredTaxType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Registered Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeAccountingAccountType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Accounting Account</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SetTriggerCode" type="qdt:AccountingDocumentCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Set Trigger Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:AccountingAccountTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AmountTypeCode" type="qdt:AccountingAmountTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Amount Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CostReferenceDimensionPattern" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Cost Reference Dimension Pattern Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeAddressType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Address</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PostcodeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Postcode</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PostOfficeBox" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Post Office Box</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuildingName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Building Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineOne" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line One</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineTwo" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Two</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineThree" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Three</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineFour" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Four</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineFive" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Five</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StreetName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Street Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CityName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">City Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CitySubDivisionName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">City Sub-Division Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountryID" type="qdt:CountryIDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountryName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountrySubDivisionID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Sub-Division Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountrySubDivisionName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Sub-Division Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AttentionOf" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Attention Of</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CareOf" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Care Of</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuildingNumber" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Building Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DepartmentName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Department Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalStreetName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Street Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CityID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">City ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:AddressTypeCodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeAllowanceChargeType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Allowance/Charge</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ChargeIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SequenceNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sequence Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PrepaidIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Prepaid Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UnitBasisAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Unit Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReasonCode" type="qdt:AllowanceChargeReasonCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Reason" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:AllowanceChargeIdentificationCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Category</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualTradeCurrencyExchange" type="ram:TradeCurrencyExchangeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Currency Exchange</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeContactType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Contact</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PersonName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Person Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DepartmentName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Department Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:ContactTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="JobTitle" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Job Title</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Responsibility" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Responsibility Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PersonID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Person ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TelephoneUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Telephone</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DirectTelephoneUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Direct Telephone</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MobileTelephoneUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Mobile Telephone</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FaxUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Fax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EmailURIUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Email Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TelexUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Telex</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VOIPUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">VOIP</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InstantMessagingUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Instant Messaging</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedContactPerson" type="ram:ContactPersonType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeCountrySubDivisionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Country Sub-Division</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeCountryType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Country</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="qdt:CountryIDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubordinateTradeCountrySubDivision" type="ram:TradeCountrySubDivisionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sub-Division</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeCurrencyExchangeType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Currency Exchange</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="SourceCurrencyCode" type="qdt:CurrencyCodeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Source Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SourceUnitBasisNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Source Unit Basis</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TargetCurrencyCode" type="qdt:CurrencyCodeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Target Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TargetUnitBaseNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Target Unit Basis</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MarketID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Market ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConversionRate" type="udt:RateType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Conversion Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConversionRateDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Conversion Rate Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AssociatedReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Associated Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeDeliveryTermsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Delivery Terms</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="DeliveryTypeCode" type="qdt:DeliveryTermsCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FunctionCode" type="qdt:DeliveryTermsFunctionCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Function Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeliveryDiscontinuationCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Delivery Discontinuation Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PartialDeliveryAllowedIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Partial Delivery Allowed Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RelevantTradeLocation" type="ram:TradeLocationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Relevant Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeLocationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Location</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="CountryID" type="qdt:CountryIDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CountryName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Country Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradePartyType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Party</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GlobalID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RoleCode" type="qdt:PartyRoleCodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Role Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RegisteredID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Registered ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Role" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Role Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLegalOrganization" type="ram:LegalOrganizationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Legal Organization</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DefinedTradeContact" type="ram:TradeContactType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Defined Contact Details</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PostalTradeAddress" type="ram:TradeAddressType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Postal Address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="URIUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">URI</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedLogisticsLocation" type="ram:LogisticsLocationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Logistics Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTaxRegistration" type="ram:TaxRegistrationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Registration</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EndPointURIUniversalCommunication" type="ram:UniversalCommunicationType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">End Point URI</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LogoAssociatedSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Logo Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradePaymentDiscountTermsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Payment Discount Terms</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="BasisDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisPeriodMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Period Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualDiscountAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Discount Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradePaymentPenaltyTermsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Payment Penalty Terms</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="BasisDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisPeriodMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Period Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualPenaltyAmount" type="udt:AmountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Penalty Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradePaymentTermsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Payment Terms</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="qdt:PaymentTermsIDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FromEventCode" type="qdt:PaymentTermsEventTimeReferenceCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">From Event Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SettlementPeriodMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Settlement Period Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DueDateDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Due Date Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:PaymentTermsTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InstructionTypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Instruction Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DirectDebitMandateID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Direct Debit Mandate ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PartialPaymentPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Partial Payment Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentMeansID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Means ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PartialPaymentAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Partial Payment Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DueDateTime" type="qdt:FormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Due Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BillStartDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Bill Start Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradePaymentPenaltyTerms" type="ram:TradePaymentPenaltyTermsType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Penalty Terms</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradePaymentDiscountTerms" type="ram:TradePaymentDiscountTermsType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Discount Terms</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayeeTradeParty" type="ram:TradePartyType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payee</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradePriceType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Price</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="qdt:PriceTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeAmount" type="udt:AmountType" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MinimumQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Minimum Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MaximumQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Maximum Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangeReason" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Change Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OrderUnitConversionFactorNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Order Unit Conversion Factor</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Type" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AppliedTradeAllowanceCharge" type="ram:TradeAllowanceChargeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applied Allowance/Charge</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValiditySpecifiedPeriod" type="ram:SpecifiedPeriodType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Validity Period</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedTradeTax" type="ram:TradeTaxType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Tax</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DeliveryTradeLocation" type="ram:TradeLocationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Delivery Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TradeComparisonReferencePrice" type="ram:ReferencePriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Comparison Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AssociatedReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Associated Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeProductInstanceType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Product Instance</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="GlobalSerialID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global Serial ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BatchID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Batch ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="KanbanID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Kanban ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SupplierAssignedSerialID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Supplier Assigned Serial ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BestBeforeDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Best Before Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExpiryDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Expiry Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellByDateTime" type="udt:DateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sell By Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SerialID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Serial ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RegistrationID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Registration ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductionSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Production Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PackagingSupplyChainEvent" type="ram:SupplyChainEventType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Packaging Event</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableMaterialGoodsCharacteristic" type="ram:MaterialGoodsCharacteristicType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Material Goods Characteristic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableProductCharacteristic" type="ram:ProductCharacteristicType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Characteristic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeProductType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Product</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GlobalID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Global ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ManufacturerAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Manufacturer Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IndustryAssignedID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Industry Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ModelID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Model ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TradeName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetWeightMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossWeightMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StatusCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Status Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductGroupID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Group ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetVolumeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Volume</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossVolumeMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Volume</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EndItemTypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">End Item Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EndItemName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">End Item Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CustomerAssignedID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customer Assigned ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BatchID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Batch ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AreaDensityMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Area Density Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UseDescription" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Use Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConciseDescription" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Concise Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalDescription" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BrandName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Brand Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubBrandName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Sub-Brand Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DrainedNetWeightMeasure" type="udt:MeasureType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Drained Net Weight</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VariableMeasureIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Variable Measure Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ConfigurableIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Configurable Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ColourCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Colour Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ColourDescription" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Colour Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RecyclingTypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Recycling Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UnitTypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Unit Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContentUnitQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Unit Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CommonName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Common Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ModelName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Model Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Designation" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Designation Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FormattedCancellationAnnouncedLaunchDateTime" type="qdt:FormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Announced Launch Cancellation Formatted Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="FormattedLatestProductDataChangeDateTime" type="qdt:FormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Latest Product Data Change Formatted Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExportIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Export Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UltimateCustomerAssignedExtensionID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Ultimate Customer Assigned Extension ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SizeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Size Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableProductCharacteristic" type="ram:ProductCharacteristicType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Characteristic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableMaterialGoodsCharacteristic" type="ram:MaterialGoodsCharacteristicType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Material Goods Characteristic</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DesignatedProductClassification" type="ram:ProductClassificationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Classification</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IndividualTradeProductInstance" type="ram:TradeProductInstanceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Individual Product Instance</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CertificationEvidenceReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Certification Evidence Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InspectionReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Inspection Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OriginTradeCountry" type="ram:TradeCountryType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Origin Country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MinimumLinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Minimum Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MaximumLinearSpatialDimension" type="ram:SpatialDimensionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Maximum Dimensions</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ManufacturerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Manufacturer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PresentationSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Presentation Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MSDSReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">MSDS Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LegalRightsOwnerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Legal Rights Owner</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BrandOwnerTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Brand Owner</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludedReferencedProduct" type="ram:ReferencedProductType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Included Product</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InformationNote" type="ram:NoteType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information Note</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerSuppliedPartsReferenceReferencedDocument" type="ram:ReferencedDocumentType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Supplied Parts Reference Document</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeSettlementFinancialCardType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Settlement Financial Card</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="MicrochipIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Microchip Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CardholderName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Cardholder Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExpiryDate" type="udt:DateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Expiry Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="VerificationNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Verification Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValidFromDateTime" type="qdt:DateOnlyFormattedDateTimeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Valid From Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditLimitAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Credit Limit Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreditAvailableAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Credit Available Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InterestRatePercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Interest Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IssuingCompanyName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Issuing Company Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeSettlementHeaderMonetarySummationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Settlement Header Monetary Summation</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="LineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AllowanceTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Allowance Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxBasisTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Basis Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RoundingAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Rounding Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrandTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Grand Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalPrepaidAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Prepaid Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalDiscountAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Discount Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalAllowanceChargeAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Allowance/Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DuePayableAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Due Payable Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RetailValueExcludingTaxInformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Retail Value Excluding Tax Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalDepositFeeInformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Deposit Fee Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductValueExcludingTobaccoTaxInformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Value Excluding Tobacco Tax Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalRetailValueInformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Retail Value Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetIncludingTaxesLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Including Taxes Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InsuranceChargeTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Insurance Charge Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludingTaxesLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Total Amount Including Taxes</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeSettlementLineMonetarySummationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Settlement Line Monetary Summation</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="LineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChargeTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Charge Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AllowanceTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Allowance Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxBasisTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Basis Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrandTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Grand Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalAllowanceChargeAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Allowance/Charge Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalRetailValueInformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Retail Value Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrossLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Gross Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="NetIncludingTaxesLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Net Including Taxes Line Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProductWeightLossInformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Product Weight Loss Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="IncludingTaxesLineTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Total Amount Including Taxes</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeSettlementPaymentMeansType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Settlement Payment Means</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="PaymentChannelCode" type="qdt:PaymentMeansChannelCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Channel Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:PaymentMeansCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GuaranteeMethodCode" type="qdt:PaymentGuaranteeMeansCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Guarantee Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PaymentMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payment Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Information" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ApplicableTradeSettlementFinancialCard" type="ram:TradeSettlementFinancialCardType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Financial Card</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayerPartyDebtorFinancialAccount" type="ram:DebtorFinancialAccountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payer Debtor Financial Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayeePartyCreditorFinancialAccount" type="ram:CreditorFinancialAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payee Creditor Financial Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayerSpecifiedDebtorFinancialInstitution" type="ram:DebtorFinancialInstitutionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payer Debtor Financial Institution</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PayeeSpecifiedCreditorFinancialInstitution" type="ram:CreditorFinancialInstitutionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Payee Creditor Financial Institution</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TradeTaxType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Trade Tax</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="CalculatedAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculated Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="qdt:TaxTypeCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExemptionReason" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Exemption Reason Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculatedRate" type="udt:RateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculated Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationSequenceNumeric" type="udt:NumericType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Calculation Sequence Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BasisAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="UnitBasisAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Unit Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="LineTotalBasisAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Line Total Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AllowanceChargeBasisAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Allowance/Charge Basis Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryCode" type="qdt:TaxCategoryCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CurrencyCode" type="qdt:CurrencyCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Jurisdiction" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Jurisdiction Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CustomsDutyIndicator" type="udt:IndicatorType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Customs Duty Indicator</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ExemptionReasonCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Exemption Reason Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxBasisAllowanceRate" type="udt:RateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basis Allowance Rate</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TaxPointDate" type="udt:DateType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Point Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Type" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="InformationAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Information Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CategoryName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Category Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DueDateTypeCode" type="qdt:TimeReferenceCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Due Date Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RateApplicablePercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Rate Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="GrandTotalAmount" type="udt:AmountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Grand Total Amount</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CalculationMethodCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Tax Calculation Method Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Trade Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ServiceSupplyTradeCountry" type="ram:TradeCountryType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Service Supply Country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerRepayableTaxSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Repayable Tax Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerPayableTaxSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Payable Tax Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SellerRefundableTaxSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Seller Refundable Tax Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerDeductibleTaxSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Deductible Tax Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BuyerNonDeductibleTaxSpecifiedTradeAccountingAccount" type="ram:TradeAccountingAccountType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Buyer Non-Deductible Tax Accounting Account</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PlaceApplicableTradeLocation" type="ram:TradeLocationType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Applicable Location</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TransportCargoInsuranceType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Transport Cargo Insurance</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="CoverageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Coverage Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CoverageDescription" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Coverage Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractGeneralConditions" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contract General Conditions Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CoverageTradeParty" type="ram:TradePartyType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Coverage Party</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TransportCargoType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Transport Cargo</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="TypeCode" type="qdt:CargoCategoryCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Identification" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Identification Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="OperationalCategoryCode" type="qdt:CargoOperationalCategoryCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Operational Category Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="StatisticalClassificationCode" type="qdt:CargoCommodityCategoryCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Statistical Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="TransportDangerousGoodsType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Transport Dangerous Goods</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="UNDGIdentificationCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">UNDG ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RegulationCode" type="qdt:DangerousGoodsRegulationCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Regulation Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RegulationName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Regulation Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TechnicalName" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Technical Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="EMSID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">EMS ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PackagingDangerLevelCode" type="qdt:DangerousGoodsPackagingLevelCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Packaging Danger Level Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="HazardClassificationID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Hazard Classification ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AdditionalHazardClassificationID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Additional Hazard Classification ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ProperShippingName" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Proper Shipping Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="UniversalCommunicationType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Communication</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="URIID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">URI</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChannelCode" type="qdt:CommunicationChannelCodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Channel Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CompleteNumber" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Complete Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="ValuationBreakdownStatementType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Valuation Breakdown Statement</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Name" type="udt:TextType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="MeasurementMethodID" type="udt:IDType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Measurement Method ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreationDateTime" type="udt:DateTimeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creation Date Time</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DefaultCurrencyCode" type="qdt:CurrencyCodeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Default Currency Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="DefaultLanguageCode" type="udt:CodeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Default Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Comment" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Comment</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestedActionCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requested Action Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PriceListID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Price List ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemGroupedWorkItem" type="ram:GroupedWorkItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Grouped Work Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ItemBasicWorkItem" type="ram:BasicWorkItemType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Basic Work Item</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TotalCalculatedPrice" type="ram:CalculatedPriceType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Total Calculated Price</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangedRecordedStatus" type="ram:RecordedStatusType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Changed Recorded Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="CreationSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Creation Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReaderSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Reader Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ReferencedSpecifiedBinaryFile" type="ram:SpecifiedBinaryFileType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Referenced Binary File</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="WorkItemComplexDescriptionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Work Item Complex Description</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="Abstract" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Abstract</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Content" type="udt:TextType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Content Text</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RequestingSpecificationQuery" type="ram:SpecificationQueryType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Requesting Specification Query</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="RespondingSpecificationResponse" type="ram:SpecificationResponseType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Responding Specification Answer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="SubsetWorkItemComplexDescription" type="ram:WorkItemComplexDescriptionType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Subset Complex Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="WorkItemDimensionType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Work Item Dimension</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ValueMeasure" type="udt:MeasureType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Measure</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ComponentWorkItemDimension" type="ram:WorkItemDimensionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Component Dimension</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="WorkItemQuantityAnalysisType">
    <xsd:annotation>
      <xsd:documentation source="BN/BN">Work Item Quantity Analysis</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ID" type="udt:IDType">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualQuantity" type="udt:QuantityType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Quantity</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="Description" type="udt:TextType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualQuantityPercent" type="udt:PercentType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Quantity Percent</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TypeCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Type Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PrimaryClassificationCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Primary Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="AlternativeClassificationCode" type="udt:CodeType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Alternative Classification Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ContractualLanguageCode" type="udt:CodeType" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Contractual Language Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ActualQuantityWorkItemDimension" type="ram:WorkItemDimensionType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Actual Quantity Dimension</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="BreakdownWorkItemQuantityAnalysis" type="ram:WorkItemQuantityAnalysisType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Quantity Breakdown Analysis</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ChangedRecordedStatus" type="ram:RecordedStatusType" minOccurs="0" maxOccurs="unbounded">
        <xsd:annotation>
          <xsd:documentation source="BN/BN">Changed Recorded Status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>
