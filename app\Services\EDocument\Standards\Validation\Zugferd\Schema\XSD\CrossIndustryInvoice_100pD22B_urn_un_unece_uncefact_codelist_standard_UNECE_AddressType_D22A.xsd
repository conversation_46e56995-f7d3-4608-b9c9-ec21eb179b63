<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm63131="urn:un:unece:uncefact:codelist:standard:UNECE:AddressType:D22A"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:AddressType:D22A"
    elementFormDefault="qualified"
    version="3.4">
  <xsd:simpleType name="AddressTypeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="1"/>
      <xsd:enumeration value="2"/>
      <xsd:enumeration value="3"/>
      <xsd:enumeration value="4"/>
      <xsd:enumeration value="5"/>
      <xsd:enumeration value="6"/>
      <xsd:enumeration value="7"/>
      <xsd:enumeration value="8"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
