<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm64431="urn:un:unece:uncefact:codelist:standard:UNECE:PaymentGuaranteeMeansCode:D22A"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:PaymentGuaranteeMeansCode:D22A"
    elementFormDefault="qualified"
    version="3.1">
  <xsd:simpleType name="PaymentGuaranteeMeansCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="1"/>
      <xsd:enumeration value="10"/>
      <xsd:enumeration value="11"/>
      <xsd:enumeration value="12"/>
      <xsd:enumeration value="13"/>
      <xsd:enumeration value="14"/>
      <xsd:enumeration value="20"/>
      <xsd:enumeration value="21"/>
      <xsd:enumeration value="23"/>
      <xsd:enumeration value="24"/>
      <xsd:enumeration value="45"/>
      <xsd:enumeration value="ZZZ"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
