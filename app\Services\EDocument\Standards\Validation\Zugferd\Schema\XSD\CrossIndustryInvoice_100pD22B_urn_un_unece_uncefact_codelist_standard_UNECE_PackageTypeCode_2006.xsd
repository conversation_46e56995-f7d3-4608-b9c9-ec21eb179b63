<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm67065="urn:un:unece:uncefact:codelist:standard:UNECE:PackageTypeCode:2006"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:PackageTypeCode:2006"
    elementFormDefault="qualified"
    version="3.3">
  <xsd:simpleType name="PackageTypeCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="43"/>
      <xsd:enumeration value="44"/>
      <xsd:enumeration value="1A"/>
      <xsd:enumeration value="1B"/>
      <xsd:enumeration value="1D"/>
      <xsd:enumeration value="1F"/>
      <xsd:enumeration value="1G"/>
      <xsd:enumeration value="1W"/>
      <xsd:enumeration value="2C"/>
      <xsd:enumeration value="3A"/>
      <xsd:enumeration value="3H"/>
      <xsd:enumeration value="4A"/>
      <xsd:enumeration value="4B"/>
      <xsd:enumeration value="4C"/>
      <xsd:enumeration value="4D"/>
      <xsd:enumeration value="4F"/>
      <xsd:enumeration value="4G"/>
      <xsd:enumeration value="4H"/>
      <xsd:enumeration value="5H"/>
      <xsd:enumeration value="5L"/>
      <xsd:enumeration value="5M"/>
      <xsd:enumeration value="6H"/>
      <xsd:enumeration value="6P"/>
      <xsd:enumeration value="7A"/>
      <xsd:enumeration value="7B"/>
      <xsd:enumeration value="8A"/>
      <xsd:enumeration value="8B"/>
      <xsd:enumeration value="8C"/>
      <xsd:enumeration value="AA"/>
      <xsd:enumeration value="AB"/>
      <xsd:enumeration value="AC"/>
      <xsd:enumeration value="AD"/>
      <xsd:enumeration value="AE"/>
      <xsd:enumeration value="AF"/>
      <xsd:enumeration value="AG"/>
      <xsd:enumeration value="AH"/>
      <xsd:enumeration value="AI"/>
      <xsd:enumeration value="AJ"/>
      <xsd:enumeration value="AL"/>
      <xsd:enumeration value="AM"/>
      <xsd:enumeration value="AP"/>
      <xsd:enumeration value="AT"/>
      <xsd:enumeration value="AV"/>
      <xsd:enumeration value="B4"/>
      <xsd:enumeration value="BA"/>
      <xsd:enumeration value="BB"/>
      <xsd:enumeration value="BC"/>
      <xsd:enumeration value="BD"/>
      <xsd:enumeration value="BE"/>
      <xsd:enumeration value="BF"/>
      <xsd:enumeration value="BG"/>
      <xsd:enumeration value="BH"/>
      <xsd:enumeration value="BI"/>
      <xsd:enumeration value="BJ"/>
      <xsd:enumeration value="BK"/>
      <xsd:enumeration value="BL"/>
      <xsd:enumeration value="BM"/>
      <xsd:enumeration value="BN"/>
      <xsd:enumeration value="BO"/>
      <xsd:enumeration value="BP"/>
      <xsd:enumeration value="BQ"/>
      <xsd:enumeration value="BR"/>
      <xsd:enumeration value="BS"/>
      <xsd:enumeration value="BT"/>
      <xsd:enumeration value="BU"/>
      <xsd:enumeration value="BV"/>
      <xsd:enumeration value="BW"/>
      <xsd:enumeration value="BX"/>
      <xsd:enumeration value="BY"/>
      <xsd:enumeration value="BZ"/>
      <xsd:enumeration value="CA"/>
      <xsd:enumeration value="CB"/>
      <xsd:enumeration value="CC"/>
      <xsd:enumeration value="CD"/>
      <xsd:enumeration value="CE"/>
      <xsd:enumeration value="CF"/>
      <xsd:enumeration value="CG"/>
      <xsd:enumeration value="CH"/>
      <xsd:enumeration value="CI"/>
      <xsd:enumeration value="CJ"/>
      <xsd:enumeration value="CK"/>
      <xsd:enumeration value="CL"/>
      <xsd:enumeration value="CM"/>
      <xsd:enumeration value="CN"/>
      <xsd:enumeration value="CO"/>
      <xsd:enumeration value="CP"/>
      <xsd:enumeration value="CQ"/>
      <xsd:enumeration value="CR"/>
      <xsd:enumeration value="CS"/>
      <xsd:enumeration value="CT"/>
      <xsd:enumeration value="CU"/>
      <xsd:enumeration value="CV"/>
      <xsd:enumeration value="CW"/>
      <xsd:enumeration value="CX"/>
      <xsd:enumeration value="CY"/>
      <xsd:enumeration value="CZ"/>
      <xsd:enumeration value="DA"/>
      <xsd:enumeration value="DB"/>
      <xsd:enumeration value="DC"/>
      <xsd:enumeration value="DG"/>
      <xsd:enumeration value="DH"/>
      <xsd:enumeration value="DI"/>
      <xsd:enumeration value="DJ"/>
      <xsd:enumeration value="DK"/>
      <xsd:enumeration value="DL"/>
      <xsd:enumeration value="DM"/>
      <xsd:enumeration value="DN"/>
      <xsd:enumeration value="DP"/>
      <xsd:enumeration value="DR"/>
      <xsd:enumeration value="DS"/>
      <xsd:enumeration value="DT"/>
      <xsd:enumeration value="DU"/>
      <xsd:enumeration value="DV"/>
      <xsd:enumeration value="DW"/>
      <xsd:enumeration value="DX"/>
      <xsd:enumeration value="DY"/>
      <xsd:enumeration value="EC"/>
      <xsd:enumeration value="ED"/>
      <xsd:enumeration value="EE"/>
      <xsd:enumeration value="EF"/>
      <xsd:enumeration value="EG"/>
      <xsd:enumeration value="EH"/>
      <xsd:enumeration value="EI"/>
      <xsd:enumeration value="EN"/>
      <xsd:enumeration value="FB"/>
      <xsd:enumeration value="FC"/>
      <xsd:enumeration value="FD"/>
      <xsd:enumeration value="FE"/>
      <xsd:enumeration value="FI"/>
      <xsd:enumeration value="FL"/>
      <xsd:enumeration value="FO"/>
      <xsd:enumeration value="FP"/>
      <xsd:enumeration value="FR"/>
      <xsd:enumeration value="FT"/>
      <xsd:enumeration value="FW"/>
      <xsd:enumeration value="FX"/>
      <xsd:enumeration value="GB"/>
      <xsd:enumeration value="GI"/>
      <xsd:enumeration value="GL"/>
      <xsd:enumeration value="GR"/>
      <xsd:enumeration value="GU"/>
      <xsd:enumeration value="GY"/>
      <xsd:enumeration value="GZ"/>
      <xsd:enumeration value="HA"/>
      <xsd:enumeration value="HB"/>
      <xsd:enumeration value="HC"/>
      <xsd:enumeration value="HG"/>
      <xsd:enumeration value="HN"/>
      <xsd:enumeration value="HR"/>
      <xsd:enumeration value="IA"/>
      <xsd:enumeration value="IB"/>
      <xsd:enumeration value="IC"/>
      <xsd:enumeration value="ID"/>
      <xsd:enumeration value="IE"/>
      <xsd:enumeration value="IF"/>
      <xsd:enumeration value="IG"/>
      <xsd:enumeration value="IH"/>
      <xsd:enumeration value="IK"/>
      <xsd:enumeration value="IL"/>
      <xsd:enumeration value="IN"/>
      <xsd:enumeration value="IZ"/>
      <xsd:enumeration value="JB"/>
      <xsd:enumeration value="JC"/>
      <xsd:enumeration value="JG"/>
      <xsd:enumeration value="JR"/>
      <xsd:enumeration value="JT"/>
      <xsd:enumeration value="JY"/>
      <xsd:enumeration value="KG"/>
      <xsd:enumeration value="KI"/>
      <xsd:enumeration value="LE"/>
      <xsd:enumeration value="LG"/>
      <xsd:enumeration value="LT"/>
      <xsd:enumeration value="LU"/>
      <xsd:enumeration value="LV"/>
      <xsd:enumeration value="LZ"/>
      <xsd:enumeration value="MA"/>
      <xsd:enumeration value="MB"/>
      <xsd:enumeration value="MC"/>
      <xsd:enumeration value="ME"/>
      <xsd:enumeration value="MR"/>
      <xsd:enumeration value="MS"/>
      <xsd:enumeration value="MT"/>
      <xsd:enumeration value="MW"/>
      <xsd:enumeration value="MX"/>
      <xsd:enumeration value="NA"/>
      <xsd:enumeration value="NE"/>
      <xsd:enumeration value="NF"/>
      <xsd:enumeration value="NG"/>
      <xsd:enumeration value="NS"/>
      <xsd:enumeration value="NT"/>
      <xsd:enumeration value="NU"/>
      <xsd:enumeration value="NV"/>
      <xsd:enumeration value="O1"/>
      <xsd:enumeration value="O2"/>
      <xsd:enumeration value="O3"/>
      <xsd:enumeration value="O4"/>
      <xsd:enumeration value="O5"/>
      <xsd:enumeration value="O6"/>
      <xsd:enumeration value="O7"/>
      <xsd:enumeration value="O8"/>
      <xsd:enumeration value="O9"/>
      <xsd:enumeration value="OA"/>
      <xsd:enumeration value="OB"/>
      <xsd:enumeration value="OC"/>
      <xsd:enumeration value="OD"/>
      <xsd:enumeration value="OE"/>
      <xsd:enumeration value="OF"/>
      <xsd:enumeration value="OG"/>
      <xsd:enumeration value="OH"/>
      <xsd:enumeration value="OI"/>
      <xsd:enumeration value="OJ"/>
      <xsd:enumeration value="OK"/>
      <xsd:enumeration value="OL"/>
      <xsd:enumeration value="OM"/>
      <xsd:enumeration value="ON"/>
      <xsd:enumeration value="OP"/>
      <xsd:enumeration value="OQ"/>
      <xsd:enumeration value="OR"/>
      <xsd:enumeration value="OS"/>
      <xsd:enumeration value="OT"/>
      <xsd:enumeration value="OU"/>
      <xsd:enumeration value="OV"/>
      <xsd:enumeration value="OW"/>
      <xsd:enumeration value="OX"/>
      <xsd:enumeration value="OY"/>
      <xsd:enumeration value="OZ"/>
      <xsd:enumeration value="P1"/>
      <xsd:enumeration value="P2"/>
      <xsd:enumeration value="P3"/>
      <xsd:enumeration value="P4"/>
      <xsd:enumeration value="PA"/>
      <xsd:enumeration value="PB"/>
      <xsd:enumeration value="PC"/>
      <xsd:enumeration value="PD"/>
      <xsd:enumeration value="PE"/>
      <xsd:enumeration value="PF"/>
      <xsd:enumeration value="PG"/>
      <xsd:enumeration value="PH"/>
      <xsd:enumeration value="PI"/>
      <xsd:enumeration value="PJ"/>
      <xsd:enumeration value="PK"/>
      <xsd:enumeration value="PL"/>
      <xsd:enumeration value="PN"/>
      <xsd:enumeration value="PO"/>
      <xsd:enumeration value="PP"/>
      <xsd:enumeration value="PR"/>
      <xsd:enumeration value="PT"/>
      <xsd:enumeration value="PU"/>
      <xsd:enumeration value="PV"/>
      <xsd:enumeration value="PX"/>
      <xsd:enumeration value="PY"/>
      <xsd:enumeration value="PZ"/>
      <xsd:enumeration value="QA"/>
      <xsd:enumeration value="QB"/>
      <xsd:enumeration value="QC"/>
      <xsd:enumeration value="QD"/>
      <xsd:enumeration value="QF"/>
      <xsd:enumeration value="QG"/>
      <xsd:enumeration value="QH"/>
      <xsd:enumeration value="QJ"/>
      <xsd:enumeration value="QK"/>
      <xsd:enumeration value="QL"/>
      <xsd:enumeration value="QM"/>
      <xsd:enumeration value="QN"/>
      <xsd:enumeration value="QP"/>
      <xsd:enumeration value="QQ"/>
      <xsd:enumeration value="QR"/>
      <xsd:enumeration value="QS"/>
      <xsd:enumeration value="RD"/>
      <xsd:enumeration value="RG"/>
      <xsd:enumeration value="RJ"/>
      <xsd:enumeration value="RK"/>
      <xsd:enumeration value="RL"/>
      <xsd:enumeration value="RO"/>
      <xsd:enumeration value="RT"/>
      <xsd:enumeration value="RZ"/>
      <xsd:enumeration value="SA"/>
      <xsd:enumeration value="SB"/>
      <xsd:enumeration value="SC"/>
      <xsd:enumeration value="SD"/>
      <xsd:enumeration value="SE"/>
      <xsd:enumeration value="SH"/>
      <xsd:enumeration value="SI"/>
      <xsd:enumeration value="SK"/>
      <xsd:enumeration value="SL"/>
      <xsd:enumeration value="SM"/>
      <xsd:enumeration value="SO"/>
      <xsd:enumeration value="SP"/>
      <xsd:enumeration value="SS"/>
      <xsd:enumeration value="ST"/>
      <xsd:enumeration value="SU"/>
      <xsd:enumeration value="SV"/>
      <xsd:enumeration value="SW"/>
      <xsd:enumeration value="SY"/>
      <xsd:enumeration value="SZ"/>
      <xsd:enumeration value="T1"/>
      <xsd:enumeration value="TB"/>
      <xsd:enumeration value="TC"/>
      <xsd:enumeration value="TD"/>
      <xsd:enumeration value="TE"/>
      <xsd:enumeration value="TG"/>
      <xsd:enumeration value="TI"/>
      <xsd:enumeration value="TK"/>
      <xsd:enumeration value="TL"/>
      <xsd:enumeration value="TN"/>
      <xsd:enumeration value="TO"/>
      <xsd:enumeration value="TR"/>
      <xsd:enumeration value="TS"/>
      <xsd:enumeration value="TT"/>
      <xsd:enumeration value="TU"/>
      <xsd:enumeration value="TV"/>
      <xsd:enumeration value="TW"/>
      <xsd:enumeration value="TY"/>
      <xsd:enumeration value="TZ"/>
      <xsd:enumeration value="UC"/>
      <xsd:enumeration value="UN"/>
      <xsd:enumeration value="VA"/>
      <xsd:enumeration value="VG"/>
      <xsd:enumeration value="VI"/>
      <xsd:enumeration value="VK"/>
      <xsd:enumeration value="VL"/>
      <xsd:enumeration value="VN"/>
      <xsd:enumeration value="VO"/>
      <xsd:enumeration value="VP"/>
      <xsd:enumeration value="VQ"/>
      <xsd:enumeration value="VR"/>
      <xsd:enumeration value="VS"/>
      <xsd:enumeration value="VY"/>
      <xsd:enumeration value="WA"/>
      <xsd:enumeration value="WB"/>
      <xsd:enumeration value="WC"/>
      <xsd:enumeration value="WD"/>
      <xsd:enumeration value="WF"/>
      <xsd:enumeration value="WG"/>
      <xsd:enumeration value="WH"/>
      <xsd:enumeration value="WJ"/>
      <xsd:enumeration value="WK"/>
      <xsd:enumeration value="WL"/>
      <xsd:enumeration value="WM"/>
      <xsd:enumeration value="WN"/>
      <xsd:enumeration value="WP"/>
      <xsd:enumeration value="WQ"/>
      <xsd:enumeration value="WR"/>
      <xsd:enumeration value="WS"/>
      <xsd:enumeration value="WT"/>
      <xsd:enumeration value="WU"/>
      <xsd:enumeration value="WV"/>
      <xsd:enumeration value="WW"/>
      <xsd:enumeration value="WX"/>
      <xsd:enumeration value="WY"/>
      <xsd:enumeration value="WZ"/>
      <xsd:enumeration value="XA"/>
      <xsd:enumeration value="XB"/>
      <xsd:enumeration value="XC"/>
      <xsd:enumeration value="XD"/>
      <xsd:enumeration value="XF"/>
      <xsd:enumeration value="XG"/>
      <xsd:enumeration value="XH"/>
      <xsd:enumeration value="XJ"/>
      <xsd:enumeration value="XK"/>
      <xsd:enumeration value="YA"/>
      <xsd:enumeration value="YB"/>
      <xsd:enumeration value="YC"/>
      <xsd:enumeration value="YD"/>
      <xsd:enumeration value="YF"/>
      <xsd:enumeration value="YG"/>
      <xsd:enumeration value="YH"/>
      <xsd:enumeration value="YJ"/>
      <xsd:enumeration value="YK"/>
      <xsd:enumeration value="YL"/>
      <xsd:enumeration value="YM"/>
      <xsd:enumeration value="YN"/>
      <xsd:enumeration value="YP"/>
      <xsd:enumeration value="YQ"/>
      <xsd:enumeration value="YR"/>
      <xsd:enumeration value="YS"/>
      <xsd:enumeration value="YT"/>
      <xsd:enumeration value="YV"/>
      <xsd:enumeration value="YW"/>
      <xsd:enumeration value="YX"/>
      <xsd:enumeration value="YY"/>
      <xsd:enumeration value="YZ"/>
      <xsd:enumeration value="ZA"/>
      <xsd:enumeration value="ZB"/>
      <xsd:enumeration value="ZC"/>
      <xsd:enumeration value="ZD"/>
      <xsd:enumeration value="ZF"/>
      <xsd:enumeration value="ZG"/>
      <xsd:enumeration value="ZH"/>
      <xsd:enumeration value="ZJ"/>
      <xsd:enumeration value="ZK"/>
      <xsd:enumeration value="ZL"/>
      <xsd:enumeration value="ZM"/>
      <xsd:enumeration value="ZN"/>
      <xsd:enumeration value="ZP"/>
      <xsd:enumeration value="ZQ"/>
      <xsd:enumeration value="ZR"/>
      <xsd:enumeration value="ZS"/>
      <xsd:enumeration value="ZT"/>
      <xsd:enumeration value="ZU"/>
      <xsd:enumeration value="ZV"/>
      <xsd:enumeration value="ZW"/>
      <xsd:enumeration value="ZX"/>
      <xsd:enumeration value="ZY"/>
      <xsd:enumeration value="ZZ"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
