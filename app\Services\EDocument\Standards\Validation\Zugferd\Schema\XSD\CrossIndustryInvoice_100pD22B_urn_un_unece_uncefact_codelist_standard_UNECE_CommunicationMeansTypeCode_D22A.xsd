<?xml version="1.0" encoding="UTF-8"?>
<xsd:schema xmlns:clm63155CommunicationChannelCode="urn:un:unece:uncefact:codelist:standard:UNECE:CommunicationMeansTypeCode:D22A"
    xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    targetNamespace="urn:un:unece:uncefact:codelist:standard:UNECE:CommunicationMeansTypeCode:D22A"
    elementFormDefault="qualified"
    version="3.4">
  <xsd:simpleType name="CommunicationMeansTypeCodeContentType">
    <xsd:restriction base="xsd:token">
      <xsd:enumeration value="AA"/>
      <xsd:enumeration value="AB"/>
      <xsd:enumeration value="AC"/>
      <xsd:enumeration value="AD"/>
      <xsd:enumeration value="AE"/>
      <xsd:enumeration value="AF"/>
      <xsd:enumeration value="AG"/>
      <xsd:enumeration value="AH"/>
      <xsd:enumeration value="AI"/>
      <xsd:enumeration value="AJ"/>
      <xsd:enumeration value="AK"/>
      <xsd:enumeration value="AL"/>
      <xsd:enumeration value="AM"/>
      <xsd:enumeration value="AN"/>
      <xsd:enumeration value="AO"/>
      <xsd:enumeration value="AP"/>
      <xsd:enumeration value="AQ"/>
      <xsd:enumeration value="AR"/>
      <xsd:enumeration value="AS"/>
      <xsd:enumeration value="AT"/>
      <xsd:enumeration value="AU"/>
      <xsd:enumeration value="AV"/>
      <xsd:enumeration value="AW"/>
      <xsd:enumeration value="CA"/>
      <xsd:enumeration value="EI"/>
      <xsd:enumeration value="EM"/>
      <xsd:enumeration value="EX"/>
      <xsd:enumeration value="FT"/>
      <xsd:enumeration value="FX"/>
      <xsd:enumeration value="GM"/>
      <xsd:enumeration value="IE"/>
      <xsd:enumeration value="IM"/>
      <xsd:enumeration value="MA"/>
      <xsd:enumeration value="PB"/>
      <xsd:enumeration value="PS"/>
      <xsd:enumeration value="SW"/>
      <xsd:enumeration value="TE"/>
      <xsd:enumeration value="TG"/>
      <xsd:enumeration value="TL"/>
      <xsd:enumeration value="TM"/>
      <xsd:enumeration value="TT"/>
      <xsd:enumeration value="TX"/>
      <xsd:enumeration value="XF"/>
      <xsd:enumeration value="XG"/>
      <xsd:enumeration value="XH"/>
      <xsd:enumeration value="XI"/>
      <xsd:enumeration value="XJ"/>
    </xsd:restriction>
  </xsd:simpleType>
</xsd:schema>
